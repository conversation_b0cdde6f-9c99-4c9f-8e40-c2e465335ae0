pub mod vehicle;
pub mod intersection;
pub mod physics;
pub mod graphics;
pub mod input;
pub mod statistics;
pub mod game;

pub use vehicle::*;
pub use intersection::*;
pub use physics::*;
pub use graphics::*;
pub use input::*;
pub use statistics::*;
pub use game::*;

#[cfg(test)]
mod tests {
    use super::*;
    use macroquad::prelude::*;

    #[test]
    fn test_vehicle_creation() {
        let vehicle = Vehicle::new(
            1,
            Direction::North,
            LaneType::Straight,
            Vec2::new(100.0, 100.0),
            60.0,
        );

        assert_eq!(vehicle.id, 1);
        assert_eq!(vehicle.direction, Direction::North);
        assert_eq!(vehicle.lane_type, LaneType::Straight);
        assert_eq!(vehicle.target_velocity, 60.0);
        assert_eq!(vehicle.state, VehicleState::Approaching);
    }

    #[test]
    fn test_intersection_creation() {
        let intersection = Intersection::new(Vec2::new(400.0, 300.0));

        assert_eq!(intersection.center, Vec2::new(400.0, 300.0));
        assert_eq!(intersection.size, 200.0);
        assert_eq!(intersection.vehicles.len(), 0);
        assert_eq!(intersection.lanes.len(), 12); // 4 directions × 3 lane types
    }

    #[test]
    fn test_physics_velocity_levels() {
        assert_eq!(physics::VELOCITY_LEVELS.len(), 3);
        assert_eq!(physics::VELOCITY_LEVELS[0], 30.0);
        assert_eq!(physics::VELOCITY_LEVELS[1], 60.0);
        assert_eq!(physics::VELOCITY_LEVELS[2], 90.0);
    }

    #[test]
    fn test_statistics_initialization() {
        let stats = Statistics::new();

        assert_eq!(stats.max_vehicles_passed, 0);
        assert_eq!(stats.close_calls, 0);
        assert_eq!(stats.total_vehicles_spawned, 0);
        assert_eq!(stats.min_velocity, f32::MAX);
    }

    #[test]
    fn test_vehicle_distance_calculation() {
        let vehicle1 = Vehicle::new(
            1,
            Direction::North,
            LaneType::Straight,
            Vec2::new(0.0, 0.0),
            60.0,
        );

        let vehicle2 = Vehicle::new(
            2,
            Direction::South,
            LaneType::Straight,
            Vec2::new(3.0, 4.0),
            60.0,
        );

        let distance = vehicle1.distance_to(&vehicle2);
        assert_eq!(distance, 5.0); // 3-4-5 triangle
    }

    #[test]
    fn test_intersection_bounds() {
        let intersection = Intersection::new(Vec2::new(100.0, 100.0));
        let (min_bounds, max_bounds) = intersection.get_intersection_bounds();

        assert_eq!(min_bounds, Vec2::new(0.0, 0.0));
        assert_eq!(max_bounds, Vec2::new(200.0, 200.0));
    }

    #[test]
    fn test_vehicle_state_transitions() {
        let mut vehicle = Vehicle::new(
            1,
            Direction::North,
            LaneType::Straight,
            Vec2::new(100.0, 100.0),
            60.0,
        );

        assert_eq!(vehicle.state, VehicleState::Approaching);

        vehicle.enter_intersection();
        assert_eq!(vehicle.state, VehicleState::InIntersection);
        assert!(vehicle.intersection_entry_time.is_some());

        vehicle.exit_intersection();
        assert_eq!(vehicle.state, VehicleState::Exiting);
        assert!(vehicle.intersection_exit_time.is_some());
    }

    #[test]
    fn test_physics_stopping_distance() {
        let stopping_distance_30 = PhysicsEngine::calculate_stopping_distance(30.0);
        let stopping_distance_60 = PhysicsEngine::calculate_stopping_distance(60.0);

        // Higher velocity should require longer stopping distance
        assert!(stopping_distance_60 > stopping_distance_30);

        // Zero velocity should have zero stopping distance
        assert_eq!(PhysicsEngine::calculate_stopping_distance(0.0), 0.0);
    }

    #[test]
    fn test_statistics_safety_rating() {
        let mut stats = Statistics::new();

        // No vehicles, should be excellent
        assert_eq!(stats.get_safety_rating(), "EXCELLENT");

        // Add some vehicles and close calls
        stats.current_vehicles_passed = 10;
        stats.close_calls = 1; // 10% close call ratio
        assert_eq!(stats.get_safety_rating(), "FAIR");

        stats.close_calls = 6; // 60% close call ratio
        assert_eq!(stats.get_safety_rating(), "DANGEROUS");
    }
}
